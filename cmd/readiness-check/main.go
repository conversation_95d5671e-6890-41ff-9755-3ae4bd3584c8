package main

import (
	"context"
	"fmt"
	"time"

	"hotel/bi/protocol"
	"hotel/bi/service"

	_ "github.com/go-sql-driver/mysql"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// ProductionReadinessChecker 生产就绪检查器
type ProductionReadinessChecker struct {
	checks []Check
}

// Check 检查项
type Check struct {
	Name     string
	Function func(ctx context.Context) error
}

// CheckResult 检查结果
type CheckResult struct {
	Name     string
	Passed   bool
	Error    string
	Duration time.Duration
}

func main() {
	checker := NewProductionReadinessChecker()
	results := checker.RunAllChecks(context.Background())

	fmt.Println("=== Production Readiness Check Results ===")

	allPassed := true
	for _, result := range results {
		status := "✅ PASS"
		if !result.Passed {
			status = "❌ FAIL"
			allPassed = false
		}

		fmt.Printf("%s %s (%.2fms)\n", status, result.Name, float64(result.Duration.Nanoseconds())/1e6)
		if result.Error != "" {
			fmt.Printf("   Error: %s\n", result.Error)
		}
	}

	fmt.Println("==========================================")
	if allPassed {
		fmt.Println("🎉 All checks passed! System is production ready.")
	} else {
		fmt.Println("⚠️  Some checks failed. Please fix issues before deployment.")
	}
}

// NewProductionReadinessChecker 创建检查器
func NewProductionReadinessChecker() *ProductionReadinessChecker {
	return &ProductionReadinessChecker{
		checks: []Check{
			{"Database Connection", checkDatabaseConnection},
			{"BI Service Initialization", checkBIServiceInit},
			{"Analytics Query Performance", checkAnalyticsPerformance},
			{"Real-time Analytics Service", checkRealTimeAnalytics},
			{"Event Tracking System", checkEventTracking},
			{"Cache Performance", checkCachePerformance},
			{"API Endpoints", checkAPIEndpoints},
			{"Memory Usage", checkMemoryUsage},
		},
	}
}

// RunAllChecks 运行所有检查
func (c *ProductionReadinessChecker) RunAllChecks(ctx context.Context) []CheckResult {
	results := make([]CheckResult, len(c.checks))

	for i, check := range c.checks {
		start := time.Now()
		err := check.Function(ctx)
		duration := time.Since(start)

		results[i] = CheckResult{
			Name:     check.Name,
			Passed:   err == nil,
			Error:    errorToString(err),
			Duration: duration,
		}
	}

	return results
}

// checkDatabaseConnection 检查数据库连接
func checkDatabaseConnection(ctx context.Context) error {
	// 使用测试数据库连接
	conn := sqlx.NewMysql("root:password@tcp(localhost:3306)/hoteldev?charset=utf8mb4&parseTime=True&loc=Local")
	if conn == nil {
		return fmt.Errorf("failed to connect to database")
	}

	// 测试连接
	_, err := conn.Exec("SELECT 1")
	if err != nil {
		return fmt.Errorf("database connection test failed: %v", err)
	}

	return nil
}

// checkBIServiceInit 检查BI服务初始化
func checkBIServiceInit(ctx context.Context) error {
	biService := service.NewBIServiceFromConfig()
	if biService == nil {
		return fmt.Errorf("failed to initialize BI service")
	}
	return nil
}

// checkAnalyticsPerformance 检查分析查询性能
func checkAnalyticsPerformance(ctx context.Context) error {
	biService := service.NewBIServiceFromConfig()

	now := time.Now()
	startDate := now.AddDate(0, 0, -7) // 最近7天

	req := &protocol.OrderAnalyticsReq{
		StartDate:   &startDate,
		EndDate:     &now,
		Granularity: "day",
	}

	start := time.Now()
	_, err := biService.GetOrderAnalytics(ctx, req)
	duration := time.Since(start)

	if err != nil {
		return fmt.Errorf("analytics query failed: %w", err)
	}

	// 检查查询时间是否在可接受范围内
	if duration > 5*time.Second {
		return fmt.Errorf("analytics query too slow: %v", duration)
	}

	return nil
}

// checkRealTimeAnalytics 检查实时分析服务
func checkRealTimeAnalytics(ctx context.Context) error {
	realtimeService := service.GetRealTimeAnalyticsService()
	if realtimeService == nil {
		return fmt.Errorf("real-time analytics service not initialized")
	}

	// 检查服务状态
	if !realtimeService.IsRunning() {
		return fmt.Errorf("real-time analytics service is not running")
	}

	// 测试强制更新
	err := realtimeService.ForceUpdate(ctx)
	if err != nil {
		return fmt.Errorf("real-time analytics force update failed: %w", err)
	}

	return nil
}

// checkEventTracking 检查事件追踪系统
func checkEventTracking(ctx context.Context) error {
	// 这里可以添加事件追踪系统的检查
	// 例如检查日志表是否存在，追踪服务是否正常等
	return nil
}

// checkCachePerformance 检查缓存性能
func checkCachePerformance(ctx context.Context) error {
	// 这里可以添加缓存系统的性能检查
	// 例如Redis连接，缓存命中率等
	return nil
}

// checkAPIEndpoints 检查API端点
func checkAPIEndpoints(ctx context.Context) error {
	// 这里可以添加API端点的健康检查
	return nil
}

// checkMemoryUsage 检查内存使用情况
func checkMemoryUsage(ctx context.Context) error {
	// 这里可以添加内存使用情况的检查
	return nil
}

// errorToString 将错误转换为字符串
func errorToString(err error) string {
	if err == nil {
		return ""
	}
	return err.Error()
}
