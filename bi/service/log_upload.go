package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/bytedance/sonic"

	"hotel/bi/domain"
	"hotel/common/log"
)

func (s *LogService) AsyncUploadLog(ctx context.Context, log *domain.HBLog) error {
	if log == nil {
		return fmt.Errorf("log cannot be nil")
	}

	select {
	case domain.GlobalHBLogChan <- log:
		return nil
	default:
		// channel is full, handle overflow, e.g., log an error or drop the log
		return fmt.Errorf("log channel is full, dropping log: %s", log.LogId)
	}
}

func (s *LogService) asyncWriteLog() {
	log.Info("async write log, interval: %s", s.cfg.BatchInterval)
	batchSize := 10
	ticker := time.NewTicker(s.cfg.BatchInterval)
	defer ticker.Stop()

	logs := make([]*domain.HBLog, 0, batchSize)

	for {
		select {
		case logEntry := <-domain.GlobalHBLogChan:
			logs = append(logs, logEntry)
			if len(logs) >= batchSize {
				s.batchInsert(context.Background(), logs)
				logs = make([]*domain.HBLog, 0, batchSize) // Reset slice
			}
		case <-ticker.C:
			if len(logs) > 0 {
				s.batchInsert(context.Background(), logs)
				logs = make([]*domain.HBLog, 0, batchSize) // Reset slice
			}
		}
	}
}

// 安全辅助函数
func safeString(s *string) string {
	if s != nil {
		return *s
	}
	return ""
}
func safeInt64(i *int64) int64 {
	if i != nil {
		return *i
	}
	return 0
}
func safeMap(m map[string]string) map[string]string {
	if m != nil {
		return m
	}
	return map[string]string{}
}

func (s *LogService) batchInsert(ctx context.Context, logs []*domain.HBLog) {
	if len(logs) == 0 {
		return
	}
	log.Infoc(ctx, "batch insert logs %d", len(logs))

	query := `INSERT INTO hb_log (timestamp, log_id, session_id, user_id, seller_entity_id, buyer_entity_id,
                    api_in_path, api_in_biz_type, api_in_biz_id, api_out_path, api_out_biz_type, api_out_biz_id, api_out_supplier, 
                    input_header, input_body, input_body_size, input_credential,
                    output_header, output_body, output_body_size, output_cost_time, output_internal_cost_time, output_http_status_code,
                    cost_time, biz_error_code, kvs) VALUES `
	var valueStrings []string
	var valueArgs []interface{}

	for _, l := range logs {
		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")

		// 结构体字段安全获取
		var (
			timestamp                                              interface{} = nil
			logId, sessionId                                       string
			userId, sellerEntityId, buyerEntityId                  int64
			apiInPath, apiInBizType, apiInBizId                    string
			apiOutPath, apiOutBizType, apiOutBizId, apiOutSupplier string
			inputBody                                              string
			inputBodySize                                          int64
			outputBody                                             string
			outputBodySize                                         int64
			outputHttpStatusCode                                   int
			outputCostTime, outputInternalCostTime, costTime       int64
		)
		if l != nil {
			timestamp = l.Timestamp
			logId = l.LogId
			sessionId = l.SessionId
			userId = l.UserId
			sellerEntityId = l.SellerEntityId
			buyerEntityId = l.BuyerEntityId
			if l.APIIn != nil {
				apiInPath = l.APIIn.Path
				apiInBizType = l.APIIn.BizType
				apiInBizId = l.APIIn.BizId
			}
			if l.APIOut != nil {
				apiOutPath = l.APIOut.Path
				apiOutBizType = l.APIOut.BizType
				apiOutBizId = l.APIOut.BizId
				apiOutSupplier = l.APIOut.Supplier
			}
			if l.Input != nil {
				inputBody = l.Input.Body
				inputBodySize = l.Input.BodySize
			}
			if l.Output != nil {
				outputBody = l.Output.Body
				outputBodySize = l.Output.BodySize
				outputHttpStatusCode = l.Output.HttpStatusCode
				outputCostTime = int64(l.Output.CostTime / time.Millisecond)
				outputInternalCostTime = int64(l.Output.InternalCostTime / time.Millisecond)
			}
			costTime = int64(l.CostTime / time.Millisecond)
		}

		// map 字段安全获取
		var inputHeaderJSON, inputCredentialJSON, outputHeaderJSON, kvsJSON string
		if l != nil && l.Input != nil && l.Input.Header != nil {
			inputHeaderJSON, _ = sonic.MarshalString(l.Input.Header)
		} else {
			inputHeaderJSON = "{}"
		}
		if l != nil && l.Input != nil && l.Input.Credential != nil {
			inputCredentialJSON, _ = sonic.MarshalString(l.Input.Credential)
		} else {
			inputCredentialJSON = "{}"
		}
		if l != nil && l.Output != nil && l.Output.Header != nil {
			outputHeaderJSON, _ = sonic.MarshalString(l.Output.Header)
		} else {
			outputHeaderJSON = "{}"
		}
		if l != nil && l.KVs != nil {
			kvsJSON, _ = sonic.MarshalString(l.KVs)
		} else {
			kvsJSON = "{}"
		}

		// 处理业务错误码
		bizErrorCode := ""
		if l != nil && l.BizError != nil {
			bizErrorCode = fmt.Sprintf("%d", l.BizError.StatusCode())
		}

		valueArgs = append(valueArgs,
			timestamp,
			logId,
			sessionId,
			userId,
			sellerEntityId,
			buyerEntityId,
			apiInPath,
			apiInBizType,
			apiInBizId,
			apiOutPath,
			apiOutBizType,
			apiOutBizId,
			apiOutSupplier,
			inputHeaderJSON,
			inputBody,
			inputBodySize,
			inputCredentialJSON,
			outputHeaderJSON,
			outputBody,
			outputBodySize,
			outputCostTime,
			outputInternalCostTime,
			outputHttpStatusCode,
			costTime,
			bizErrorCode,
			kvsJSON)
	}

	stmt := fmt.Sprintf(query+"%s", strings.Join(valueStrings, ","))

	_, err := s.conn.ExecCtx(ctx, stmt, valueArgs...)
	if err != nil {
		log.Infoc(ctx, "failed to batch insert logs", "error", err)
	}
}
