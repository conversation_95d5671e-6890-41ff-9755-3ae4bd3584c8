package service

import (
	"context"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	"hotel/bi/protocol"
	"hotel/common/i18n"
	"hotel/common/money"
)

func TestOrderBIService_GetOrderAnalytics(t *testing.T) {
	mockey.PatchConvey("OrderBIService GetOrderAnalytics", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		startDate := time.Now().AddDate(0, -1, 0)
		endDate := time.Now()

		mockResp := &protocol.OrderAnalyticsResp{
			Overview: &protocol.OrderOverview{
				TotalOrders:      100,
				TotalRevenue:     money.Money{Amount: 1000000, Currency: "USD"},
				TotalProfit:      money.Money{Amount: 200000, Currency: "USD"},
				CompletionRate:   85.5,
				CancellationRate: 8.2,
				GrowthRate:       12.5,
			},
			TrendData: []*protocol.OrderTrendData{
				{
					Date:         startDate,
					OrderCount:   10,
					Revenue:      money.Money{Amount: 100000, Currency: "USD"},
					Profit:       money.Money{Amount: 20000, Currency: "USD"},
					BookingCount: 8,
					CancelCount:  1,
				},
			},
			StatusBreakdown: []*protocol.OrderStatusStat{
				{
					Status:     1,
					StatusName: i18n.I18N{Zh: "已创建", En: "Created"},
					Count:      20,
					Percentage: 20.0,
					Revenue:    money.Money{Amount: 200000, Currency: "USD"},
				},
			},
		}
		mockey.Mock((*OrderAnalyticsService).GetOrderAnalytics).Return(mockResp, nil).Build()

		req := &protocol.OrderAnalyticsReq{
			StartDate:   &startDate,
			EndDate:     &endDate,
			Granularity: "day",
		}
		resp, err := service.GetOrderAnalytics(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, mockResp.Overview.TotalOrders, resp.Overview.TotalOrders)
		assert.Equal(t, mockResp.Overview.TotalRevenue.Amount, resp.Overview.TotalRevenue.Amount)
		assert.Len(t, resp.TrendData, 1)
		assert.Len(t, resp.StatusBreakdown, 1)
	})
}

func TestOrderBIService_GetOrderMetrics_Overview(t *testing.T) {
	mockey.PatchConvey("OrderBIService GetOrderMetrics Overview", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		mockAnalyticsResp := &protocol.OrderAnalyticsResp{
			Overview: &protocol.OrderOverview{
				TotalOrders:  50,
				TotalRevenue: money.Money{Amount: 500000, Currency: "USD"},
			},
		}
		mockey.Mock((*OrderAnalyticsService).GetOrderAnalytics).Return(mockAnalyticsResp, nil).Build()

		req := &protocol.OrderMetricsReq{
			MetricType: "overview",
			StartDate:  &time.Time{},
			EndDate:    &time.Time{},
			Filters:    map[string]interface{}{"entityId": float64(123)},
		}

		resp, err := service.GetOrderMetrics(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "overview", resp.MetricType)
		assert.NotNil(t, resp.Data)

		overview, ok := resp.Data.(*protocol.OrderOverview)
		assert.True(t, ok)
		assert.Equal(t, int64(50), overview.TotalOrders)
	})
}

func TestOrderBIService_GetOrderMetrics_Trend(t *testing.T) {
	mockey.PatchConvey("OrderBIService GetOrderMetrics Trend", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		mockAnalyticsResp := &protocol.OrderAnalyticsResp{
			TrendData: []*protocol.OrderTrendData{
				{
					Date:       time.Now(),
					OrderCount: 20,
					Revenue:    money.Money{Amount: 200000, Currency: "USD"},
				},
			},
		}
		mockey.Mock((*OrderAnalyticsService).GetOrderAnalytics).Return(mockAnalyticsResp, nil).Build()

		req := &protocol.OrderMetricsReq{
			MetricType: "trend",
		}

		resp, err := service.GetOrderMetrics(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, "trend", resp.MetricType)

		trendData, ok := resp.Data.([]*protocol.OrderTrendData)
		assert.True(t, ok)
		assert.Len(t, trendData, 1)
		assert.Equal(t, int64(20), trendData[0].OrderCount)
	})
}

func TestOrderBIService_GetOrderMetrics_Status(t *testing.T) {
	mockey.PatchConvey("OrderBIService GetOrderMetrics Status", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		mockStatusData := []*protocol.OrderStatusStat{
			{
				Status:     5,
				StatusName: i18n.I18N{Zh: "已完成", En: "Completed"},
				Count:      30,
				Percentage: 60.0,
			},
		}

		mockAnalyticsResp := &protocol.OrderAnalyticsResp{
			StatusBreakdown: mockStatusData,
		}
		mockey.Mock((*OrderAnalyticsService).GetOrderAnalytics).Return(mockAnalyticsResp, nil).Build()

		req := &protocol.OrderMetricsReq{
			MetricType: "status",
		}

		resp, err := service.GetOrderMetrics(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, "status", resp.MetricType)

		statusData, ok := resp.Data.([]*protocol.OrderStatusStat)
		assert.True(t, ok)
		assert.Len(t, statusData, 1)
		assert.Equal(t, int64(5), statusData[0].Status)
		assert.Equal(t, int64(30), statusData[0].Count)
	})
}

func TestOrderBIService_GetOrderMetrics_Revenue(t *testing.T) {
	mockey.PatchConvey("OrderBIService GetOrderMetrics Revenue", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		mockRevenueAnalysis := &protocol.RevenueAnalysis{
			TotalRevenue: money.Money{Amount: 1000000, Currency: "USD"},
			TotalProfit:  money.Money{Amount: 200000, Currency: "USD"},
			ProfitMargin: 20.0,
		}

		mockAnalyticsResp := &protocol.OrderAnalyticsResp{
			RevenueAnalysis: mockRevenueAnalysis,
		}
		mockey.Mock((*OrderAnalyticsService).GetOrderAnalytics).Return(mockAnalyticsResp, nil).Build()

		req := &protocol.OrderMetricsReq{
			MetricType: "revenue",
		}

		resp, err := service.GetOrderMetrics(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, "revenue", resp.MetricType)

		revenueData, ok := resp.Data.(*protocol.RevenueAnalysis)
		assert.True(t, ok)
		assert.Equal(t, int64(1000000), revenueData.TotalRevenue.Amount)
		assert.Equal(t, float64(20.0), revenueData.ProfitMargin)
	})
}

func TestOrderBIService_GetOrderMetrics_UnsupportedType(t *testing.T) {
	mockey.PatchConvey("OrderBIService GetOrderMetrics UnsupportedType", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		req := &protocol.OrderMetricsReq{
			MetricType: "invalid_type",
		}

		resp, err := service.GetOrderMetrics(ctx, req)
		assert.Error(t, err)
		assert.Nil(t, resp)
		assert.Contains(t, err.Error(), "unsupported metric type")
	})
}

func TestOrderBIService_GetRealTimeMetrics(t *testing.T) {
	mockey.PatchConvey("OrderBIService GetRealTimeMetrics", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		mockResp := &protocol.RealTimeMetricsResp{
			TodayOrders:      25,
			TodayRevenue:     money.Money{Amount: 250000, Currency: "USD"},
			ActiveBookings:   20,
			PendingOrders:    3,
			CompletionRate:   85.5,
			CancellationRate: 8.2,
			UpdateTime:       time.Now(),
		}
		mockey.Mock((*OrderAnalyticsService).GetRealTimeMetrics).Return(mockResp, nil).Build()

		req := &protocol.RealTimeMetricsReq{
			Metrics: []string{"todayOrders", "todayRevenue"},
		}

		resp, err := service.GetRealTimeMetrics(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, int64(25), resp.TodayOrders)
		assert.Equal(t, int64(250000), resp.TodayRevenue.Amount)
		assert.Equal(t, "USD", resp.TodayRevenue.Currency)
	})
}

func TestOrderBIService_ExportData(t *testing.T) {
	mockey.PatchConvey("OrderBIService ExportData", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		req := &protocol.ExportReq{
			ExportType: "excel",
			DataType:   "orders",
			StartDate:  &time.Time{},
			EndDate:    &time.Time{},
			Filters:    map[string]interface{}{"status": "completed"},
			Fields:     []string{"orderId", "amount", "status"},
		}

		resp, err := service.ExportData(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Contains(t, resp.FileName, "order_orders_")
		assert.Contains(t, resp.FileName, ".excel")
		assert.NotEmpty(t, resp.FileUrl)
		assert.Greater(t, resp.FileSize, int64(0))
	})
}

func TestOrderBIService_GetOrderMetrics_DefaultDates(t *testing.T) {
	mockey.PatchConvey("OrderBIService GetOrderMetrics DefaultDates", t, func() {
		ctx := context.Background()
		analyticsService := &OrderAnalyticsService{}
		service := NewOrderBIService(analyticsService)

		mockAnalyticsResp := &protocol.OrderAnalyticsResp{
			Overview: &protocol.OrderOverview{
				TotalOrders:  100,
				TotalRevenue: money.Money{Amount: 1000000, Currency: "USD"},
			},
		}
		mockey.Mock((*OrderAnalyticsService).GetOrderAnalytics).Return(mockAnalyticsResp, nil).Build()

		req := &protocol.OrderMetricsReq{
			MetricType: "overview",
		}

		resp, err := service.GetOrderMetrics(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "overview", resp.MetricType)
	})
}
