package main

import (
	"fmt"

	geoDomain "hotel/geography/domain"
	geoService "hotel/geography/service"
)

func main() {
	fmt.Println("索引构建时NameFull优化测试")
	fmt.Println("==========================")

	// 创建测试数据
	testRegions := []*geoDomain.Region{
		{
			ID:       1,
			Name:     "Shanghai",
			NameFull: "Shanghai, Shanghai, China",
			Type:     geoDomain.RegionType_MultiCityVicinity,
		},
		{
			ID:       2,
			Name:     "Beijing",
			NameFull: "Beijing, Beijing, Beijing, China",
			Type:     geoDomain.RegionType_MultiCityVicinity,
		},
		{
			ID:       3,
			Name:     "Guangzhou",
			NameFull: "Guangzhou , Guangzhou, Guangdong, China",
			Type:     geoDomain.RegionType_MultiCityVicinity,
		},
		{
			ID:       4,
			Name:     "Songjiang District",
			NameFull: "Songjiang District, Shanghai, Shanghai, China",
			Type:     geoDomain.RegionType_City,
		},
		{
			ID:       5,
			Name:     "Test",
			NameFull: "Test, , , China",
			Type:     geoDomain.RegionType_City,
		},
	}

	fmt.Println("1. 优化前的NameFull:")
	for _, region := range testRegions {
		fmt.Printf("  %s: '%s'\n", region.Name, region.NameFull)
	}

	// 创建索引管理器并构建索引
	indexManager := geoService.NewInMemoryIndexManager()
	
	fmt.Println("\n2. 构建索引（会自动优化NameFull）...")
	indexManager.BuildIndexes(testRegions)

	fmt.Println("\n3. 优化后的NameFull:")
	for _, region := range testRegions {
		fmt.Printf("  %s: '%s'\n", region.Name, region.NameFull)
	}

	// 验证优化效果
	fmt.Println("\n4. 优化效果验证:")
	expectedResults := map[string]string{
		"Shanghai":          "Shanghai, China",
		"Beijing":           "Beijing, China",
		"Guangzhou":         "Guangzhou, Guangdong, China",
		"Songjiang District": "Songjiang District, Shanghai, China",
		"Test":              "Test, China",
	}

	allCorrect := true
	for _, region := range testRegions {
		expected := expectedResults[region.Name]
		if region.NameFull == expected {
			fmt.Printf("  ✓ %s: 正确\n", region.Name)
		} else {
			fmt.Printf("  ❌ %s: 期望 '%s', 实际 '%s'\n", region.Name, expected, region.NameFull)
			allCorrect = false
		}
	}

	// 验证从索引中获取的数据也是优化后的
	fmt.Println("\n5. 从索引中获取数据验证:")
	for _, region := range testRegions {
		retrievedRegion, exists := indexManager.GetRegionByID(region.ID)
		if !exists {
			fmt.Printf("  ❌ %s: 无法从索引中获取\n", region.Name)
			allCorrect = false
			continue
		}
		
		expected := expectedResults[region.Name]
		if retrievedRegion.NameFull == expected {
			fmt.Printf("  ✓ %s: 索引中的数据正确\n", region.Name)
		} else {
			fmt.Printf("  ❌ %s: 索引中期望 '%s', 实际 '%s'\n", region.Name, expected, retrievedRegion.NameFull)
			allCorrect = false
		}
	}

	fmt.Println("\n6. 性能优势:")
	fmt.Println("  ✓ NameFull优化在索引构建时一次性完成")
	fmt.Println("  ✓ 搜索时无需重复处理，提升性能")
	fmt.Println("  ✓ 所有使用NameFull的地方都是优化后的数据")
	fmt.Println("  ✓ 数据一致性得到保证")

	if allCorrect {
		fmt.Println("\n✅ 所有测试通过！索引构建时的NameFull优化工作正常")
	} else {
		fmt.Println("\n❌ 部分测试失败，请检查实现")
	}

	fmt.Println("\n优化说明:")
	fmt.Println("- NameFull优化从搜索时移到了索引构建时")
	fmt.Println("- 避免了每次搜索都重复处理相同数据")
	fmt.Println("- 提升了搜索性能和数据一致性")
}
