package main

import (
	"context"
	"fmt"
	"log"

	"hotel/common/pagehelper"
	"hotel/common/types"
	"hotel/content/protocol"
	"hotel/content/service"
)

// 这个示例展示了如何使用新的层级搜索功能
func main() {
	fmt.Println("酒店地理层级搜索示例")
	fmt.Println("===================")

	// 注意：这个示例需要完整的服务初始化，包括数据库连接和geography service
	// 在实际使用中，你需要正确初始化所有依赖

	// 示例1：搜索上海市的酒店
	// 现在会自动包含松江区、黄浦区、徐汇区等所有子区域的酒店
	example1()

	// 示例2：搜索特定区县的酒店
	// 会包含该区县及其子区域的酒店
	example2()

	// 示例3：多个region的搜索
	// 会扩展所有region的层级关系
	example3()
}

func example1() {
	fmt.Println("\n示例1: 搜索上海市的酒店")
	fmt.Println("------------------------")

	// 假设上海市的region ID是 1000
	shanghaiRegionId := types.ID(1000)

	req := &protocol.ListHotelByRegionIdsReq{
		RegionIds: types.IDs{shanghaiRegionId},
		PageReq: pagehelper.PageReq{
			PageSize: 20,
		},
	}

	fmt.Printf("原始搜索: RegionIds = [%d] (上海市)\n", shanghaiRegionId)
	fmt.Println("层级搜索会自动扩展为:")
	fmt.Println("  - 1000 (上海市)")
	fmt.Println("  - 1001 (松江区)")
	fmt.Println("  - 1002 (黄浦区)")
	fmt.Println("  - 1003 (徐汇区)")
	fmt.Println("  - ... (其他区县)")

	// 在实际使用中：
	// contentService := service.NewContentService()
	// resp, err := contentService.ListHotelByRegionIds(context.Background(), req)
	// if err != nil {
	//     log.Fatal(err)
	// }
	// fmt.Printf("找到 %d 个酒店\n", len(resp.Hotels))

	fmt.Println("结果: 现在能找到所有上海市区县的酒店，而不仅仅是city_region_id=1000的酒店")
}

func example2() {
	fmt.Println("\n示例2: 搜索松江区的酒店")
	fmt.Println("------------------------")

	// 假设松江区的region ID是 1001
	songjiangRegionId := types.ID(1001)

	req := &protocol.ListHotelByRegionIdsReq{
		RegionIds: types.IDs{songjiangRegionId},
		PageReq: pagehelper.PageReq{
			PageSize: 10,
		},
	}

	fmt.Printf("原始搜索: RegionIds = [%d] (松江区)\n", songjiangRegionId)
	fmt.Println("层级搜索会自动扩展为:")
	fmt.Println("  - 1001 (松江区)")
	fmt.Println("  - 1000 (上海市) - 祖先region")
	fmt.Println("  - 1011, 1012, ... (松江区下的街道) - 后代region")

	fmt.Println("结果: 能找到松江区及其子区域的酒店，同时也能匹配到标记为上海市的酒店")
}

func example3() {
	fmt.Println("\n示例3: 多个region的搜索")
	fmt.Println("------------------------")

	// 同时搜索上海市和北京市
	regionIds := types.IDs{1000, 2000} // 上海市, 北京市

	req := &protocol.ListHotelByRegionIdsReq{
		RegionIds: regionIds,
		PageReq: pagehelper.PageReq{
			PageSize: 50,
		},
	}

	fmt.Printf("原始搜索: RegionIds = %v (上海市, 北京市)\n", regionIds)
	fmt.Println("层级搜索会自动扩展为:")
	fmt.Println("  上海市层级:")
	fmt.Println("    - 1000 (上海市)")
	fmt.Println("    - 1001, 1002, 1003, ... (上海市的各区县)")
	fmt.Println("  北京市层级:")
	fmt.Println("    - 2000 (北京市)")
	fmt.Println("    - 2001, 2002, 2003, ... (北京市的各区县)")

	fmt.Println("结果: 能找到两个城市及其所有区县的酒店")
}

// 演示层级搜索的优势
func demonstrateAdvantages() {
	fmt.Println("\n层级搜索的优势")
	fmt.Println("==============")

	fmt.Println("1. 解决数据不精确问题:")
	fmt.Println("   - 酒店A的city_region_id=松江区")
	fmt.Println("   - 酒店B的city_region_id=上海市")
	fmt.Println("   - 搜索'上海'时，两个酒店都能被找到")

	fmt.Println("\n2. 兼容供应商数据差异:")
	fmt.Println("   - 供应商1: 酒店归属到具体区县")
	fmt.Println("   - 供应商2: 酒店归属到城市级别")
	fmt.Println("   - 层级搜索能统一处理这些差异")

	fmt.Println("\n3. 提升搜索覆盖率:")
	fmt.Println("   - 用户搜索'上海'")
	fmt.Println("   - 系统自动包含所有相关的地理区域")
	fmt.Println("   - 不会遗漏任何相关酒店")

	fmt.Println("\n4. 保持向后兼容:")
	fmt.Println("   - API接口完全不变")
	fmt.Println("   - 现有代码无需修改")
	fmt.Println("   - 自动获得层级搜索能力")
}

// 性能考虑
func performanceConsiderations() {
	fmt.Println("\n性能考虑")
	fmt.Println("========")

	fmt.Println("1. 缓存优化:")
	fmt.Println("   - 地理层级关系相对稳定")
	fmt.Println("   - GetExpandedRegionIds结果可以缓存")

	fmt.Println("\n2. 查询优化:")
	fmt.Println("   - 使用IN查询匹配多个region ID")
	fmt.Println("   - 利用city_region_id索引")

	fmt.Println("\n3. 容错机制:")
	fmt.Println("   - 层级扩展失败时自动fallback")
	fmt.Println("   - 保证搜索功能的可用性")
}

// 实际使用示例（需要完整的服务初始化）
func realWorldExample() {
	fmt.Println("\n实际使用示例")
	fmt.Println("============")

	// 这是一个完整的使用示例，需要在有完整服务的环境中运行
	/*
	// 1. 初始化服务
	contentService := service.NewContentService()
	ctx := context.Background()

	// 2. 搜索上海市的酒店
	req := &protocol.ListHotelByRegionIdsReq{
		RegionIds: types.IDs{1000}, // 上海市
		PageReq: pagehelper.PageReq{
			PageSize: 20,
		},
	}

	// 3. 执行搜索（现在会自动使用层级搜索）
	resp, err := contentService.ListHotelByRegionIds(ctx, req)
	if err != nil {
		log.Fatalf("搜索失败: %v", err)
	}

	// 4. 处理结果
	fmt.Printf("找到 %d 个酒店\n", len(resp.Hotels))
	for i, hotel := range resp.Hotels {
		fmt.Printf("%d. %s (RegionId: %d)\n", 
			i+1, hotel.Name, hotel.RegionId)
	}
	*/

	fmt.Println("// 代码示例见注释")
}
