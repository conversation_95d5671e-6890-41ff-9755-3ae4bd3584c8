package test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/types"
	"hotel/user/cache"
	"hotel/user/config"
	"hotel/user/domain"
	"hotel/user/mysql"
	"hotel/user/protocol"
	"hotel/user/service"
)

// TestSetup 测试设置
type TestSetup struct {
	userSrv   *service.UserService
	dao       *mysql.Dao
	userCache cache.UserCache
	testUser  *domain.User
}

// setupTest 设置测试环境
func setupTest(t *testing.T) *TestSetup {
	// 加载配置
	var cfg config.Config
	conf.MustLoad("../config/config.yaml", &cfg)

	// 创建数据库连接
	userDB := sqlx.NewMysql(cfg.MySQL.User)
	dao := mysql.NewDao(userDB)

	// 创建Redis连接
	rds := redis.MustNewRedis(redis.RedisConf{
		Host: "localhost:6379",
		Type: "node",
	})
	userCache := cache.NewUserCache(*rds)

	// 创建用户服务
	userSrv := service.NewUserService()

	// 创建测试用户
	testUser := &domain.User{
		UserBasic: &domain.UserBasic{
			ID:       types.ID(999),
			Key:      "<EMAIL>",
			Username: "test_performance",
			Secret:   "$2a$10$hash",
		},
		UserEntityConnections: []*domain.UserEntityConnection{},
	}

	return &TestSetup{
		userSrv:   userSrv,
		dao:       dao,
		userCache: userCache,
		testUser:  testUser,
	}
}

// BenchmarkResult 基准测试结果
type BenchmarkResult struct {
	TestName      string        `json:"test_name"`
	TotalRequests int           `json:"total_requests"`
	TotalDuration time.Duration `json:"total_duration"`
	AvgDuration   time.Duration `json:"avg_duration"`
	QPS           float64       `json:"qps"`
}

// TestOriginalVsOptimizedLogin 对比原始和优化后的登录性能
func TestOriginalVsOptimizedLogin(t *testing.T) {
	setup := setupTest(t)
	ctx := context.Background()

	// 确保测试用户存在
	err := setup.dao.Transact(ctx, func(ctx context.Context, tx sqlx.Session) error {
		return setup.dao.User.TxUpsertUser(ctx, tx, setup.testUser.UserBasic)
	})
	assert.NoError(t, err)

	t.Run("Original Login Performance", func(t *testing.T) {
		result := benchmarkOriginalLogin(t, setup, 100)
		t.Logf("Original Login - QPS: %.2f, Avg: %v", result.QPS, result.AvgDuration)

		// 基本性能要求
		assert.Greater(t, result.QPS, 10.0, "Original QPS should be greater than 10")
		assert.Less(t, result.AvgDuration.Milliseconds(), int64(1000), "Original response time should be less than 1s")
	})

	t.Run("Optimized Login Performance", func(t *testing.T) {
		result := benchmarkOptimizedLogin(t, setup, 100)
		t.Logf("Optimized Login - QPS: %.2f, Avg: %v", result.QPS, result.AvgDuration)

		// 优化后的性能要求
		assert.Greater(t, result.QPS, 20.0, "Optimized QPS should be greater than 20")
		assert.Less(t, result.AvgDuration.Milliseconds(), int64(500), "Optimized response time should be less than 500ms")
	})
}

// benchmarkOriginalLogin 基准测试原始登录
func benchmarkOriginalLogin(t *testing.T, setup *TestSetup, requests int) *BenchmarkResult {
	ctx := context.Background()
	durations := make([]time.Duration, 0, requests)

	startTime := time.Now()

	for i := 0; i < requests; i++ {
		reqStart := time.Now()

		_, err := setup.userSrv.GetUser(ctx, &protocol.GetUserReq{
			Key: setup.testUser.Key,
		})

		reqDuration := time.Since(reqStart)
		durations = append(durations, reqDuration)

		if err != nil {
			t.Logf("Request %d failed: %v", i, err)
		}
	}

	totalDuration := time.Since(startTime)
	avgDuration := totalDuration / time.Duration(requests)
	qps := float64(requests) / totalDuration.Seconds()

	return &BenchmarkResult{
		TestName:      "Original Login",
		TotalRequests: requests,
		TotalDuration: totalDuration,
		AvgDuration:   avgDuration,
		QPS:           qps,
	}
}

// benchmarkOptimizedLogin 基准测试优化后的登录
func benchmarkOptimizedLogin(t *testing.T, setup *TestSetup, requests int) *BenchmarkResult {
	ctx := context.Background()
	durations := make([]time.Duration, 0, requests)

	startTime := time.Now()

	for i := 0; i < requests; i++ {
		reqStart := time.Now()

		_, err := setup.userSrv.GetUserOptimized(ctx, &protocol.GetUserReq{
			Key: setup.testUser.Key,
		})

		reqDuration := time.Since(reqStart)
		durations = append(durations, reqDuration)

		if err != nil {
			t.Logf("Request %d failed: %v", i, err)
		}
	}

	totalDuration := time.Since(startTime)
	avgDuration := totalDuration / time.Duration(requests)
	qps := float64(requests) / totalDuration.Seconds()

	return &BenchmarkResult{
		TestName:      "Optimized Login",
		TotalRequests: requests,
		TotalDuration: totalDuration,
		AvgDuration:   avgDuration,
		QPS:           qps,
	}
}

// TestCachePerformance 测试缓存性能
func TestCachePerformance(t *testing.T) {
	setup := setupTest(t)
	ctx := context.Background()

	t.Run("Cache Hit Performance", func(t *testing.T) {
		// 预热缓存
		setup.userCache.SetUser(ctx, setup.testUser.ID, setup.testUser)

		result := benchmarkCacheHit(t, setup, 100)
		t.Logf("Cache Hit - QPS: %.2f, Avg: %v", result.QPS, result.AvgDuration)

		// 缓存命中性能要求
		assert.Greater(t, result.QPS, 100.0, "Cache hit QPS should be greater than 100")
		assert.Less(t, result.AvgDuration.Milliseconds(), int64(50), "Cache hit response time should be less than 50ms")
	})
}

// benchmarkCacheHit 基准测试缓存命中
func benchmarkCacheHit(t *testing.T, setup *TestSetup, requests int) *BenchmarkResult {
	ctx := context.Background()
	durations := make([]time.Duration, 0, requests)

	startTime := time.Now()

	for i := 0; i < requests; i++ {
		reqStart := time.Now()

		_, err := setup.userCache.GetUser(ctx, setup.testUser.ID)

		reqDuration := time.Since(reqStart)
		durations = append(durations, reqDuration)

		if err != nil {
			t.Logf("Cache request %d failed: %v", i, err)
		}
	}

	totalDuration := time.Since(startTime)
	avgDuration := totalDuration / time.Duration(requests)
	qps := float64(requests) / totalDuration.Seconds()

	return &BenchmarkResult{
		TestName:      "Cache Hit",
		TotalRequests: requests,
		TotalDuration: totalDuration,
		AvgDuration:   avgDuration,
		QPS:           qps,
	}
}

// TestDatabaseIndexEffectiveness 测试数据库索引效果
func TestDatabaseIndexEffectiveness(t *testing.T) {
	setup := setupTest(t)
	ctx := context.Background()

	t.Run("User Key Index Performance", func(t *testing.T) {
		// 测试按key查询的性能
		startTime := time.Now()

		for i := 0; i < 50; i++ {
			_, err := setup.dao.User.GetUserByKey(ctx, setup.testUser.Key)
			if err != nil && err != mysql.ErrNotFound {
				t.Logf("Query %d failed: %v", i, err)
			}
		}

		duration := time.Since(startTime)
		avgDuration := duration / 50

		t.Logf("Key index query - Total: %v, Avg: %v", duration, avgDuration)

		// 索引查询应该很快
		assert.Less(t, avgDuration.Milliseconds(), int64(100), "Indexed query should be fast")
	})
}

// TestPerformanceEndToEndOptimization 端到端优化测试
func TestPerformanceEndToEndOptimization(t *testing.T) {
	setup := setupTest(t)
	ctx := context.Background()

	// 确保测试用户存在
	err := setup.dao.Transact(ctx, func(ctx context.Context, tx sqlx.Session) error {
		return setup.dao.User.TxUpsertUser(ctx, tx, setup.testUser.UserBasic)
	})
	assert.NoError(t, err)

	t.Run("Complete Login Flow", func(t *testing.T) {
		// 模拟完整的登录流程
		startTime := time.Now()

		// 1. 获取用户基本信息
		userBasic, err := setup.userSrv.GetUserBasicOptimized(ctx, setup.testUser.Key)
		assert.NoError(t, err)
		assert.NotNil(t, userBasic)

		// 2. 获取完整用户信息
		userResp, err := setup.userSrv.GetUserOptimized(ctx, &protocol.GetUserReq{
			Key: setup.testUser.Key,
		})
		assert.NoError(t, err)
		assert.NotNil(t, userResp.User)

		// 3. 缓存用户信息
		err = setup.userCache.SetUser(ctx, setup.testUser.ID, userResp.User)
		assert.NoError(t, err)

		// 4. 从缓存获取用户信息
		cachedUser, err := setup.userCache.GetUser(ctx, setup.testUser.ID)
		assert.NoError(t, err)
		assert.Equal(t, userResp.User.ID, cachedUser.ID)

		totalDuration := time.Since(startTime)
		t.Logf("Complete login flow took: %v", totalDuration)

		// 完整流程应该在合理时间内完成
		assert.Less(t, totalDuration.Milliseconds(), int64(1000), "Complete flow should finish within 1s")
	})
}

// BenchmarkUserQuery 基准测试用户查询
func BenchmarkUserQuery(b *testing.B) {
	// 这里可以添加Go的标准基准测试
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// 执行查询操作
		_ = fmt.Sprintf("user_query_%d", i)
	}
}
