package test

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/types"
	"hotel/user/cache"
	"hotel/user/config"
	"hotel/user/domain"
	"hotel/user/mysql"
)

// StandaloneTestSetup 独立测试设置
type StandaloneTestSetup struct {
	dao           *mysql.Dao
	userCache     cache.UserCache
	testUserID    types.ID
	testUserKey   string
}

// setupStandaloneTest 设置独立测试环境
func setupStandaloneTest(t *testing.T) *StandaloneTestSetup {
	// 加载配置
	var cfg config.Config
	conf.MustLoad("../config/config.yaml", &cfg)
	
	// 创建数据库连接
	userDB := sqlx.NewMysql(cfg.MySQL.User)
	dao := mysql.NewDao(userDB)
	
	// 创建Redis连接
	rds := redis.MustNewRedis(redis.RedisConf{
		Host: "localhost:6379",
		Type: "node",
	})
	userCache := cache.NewUserCache(*rds)
	
	return &StandaloneTestSetup{
		dao:         dao,
		userCache:   userCache,
		testUserID:  types.ID(999),
		testUserKey: "<EMAIL>",
	}
}

// TestStandaloneCachePerformance 独立缓存性能测试
func TestStandaloneCachePerformance(t *testing.T) {
	setup := setupStandaloneTest(t)
	ctx := context.Background()
	
	// 创建测试用户对象
	testUser := &domain.User{
		UserBasic: &domain.UserBasic{
			ID:       setup.testUserID,
			Key:      setup.testUserKey,
			Username: "test_performance",
			Secret:   "$2a$10$hash",
		},
		UserEntityConnections: []*domain.UserEntityConnection{},
	}
	
	t.Run("Cache Operations Performance", func(t *testing.T) {
		// 测试缓存设置
		setStart := time.Now()
		err := setup.userCache.SetUser(ctx, setup.testUserID, testUser)
		setDuration := time.Since(setStart)
		
		assert.NoError(t, err)
		t.Logf("Cache Set: %v", setDuration)
		assert.Less(t, setDuration.Milliseconds(), int64(50), "Cache set should be fast")
		
		// 测试缓存获取
		getStart := time.Now()
		cachedUser, err := setup.userCache.GetUser(ctx, setup.testUserID)
		getDuration := time.Since(getStart)
		
		assert.NoError(t, err)
		assert.NotNil(t, cachedUser)
		assert.Equal(t, testUser.ID, cachedUser.ID)
		t.Logf("Cache Get: %v", getDuration)
		assert.Less(t, getDuration.Milliseconds(), int64(10), "Cache get should be very fast")
		
		// 测试缓存删除
		delStart := time.Now()
		err = setup.userCache.DelUser(ctx, setup.testUserID)
		delDuration := time.Since(delStart)
		
		assert.NoError(t, err)
		t.Logf("Cache Delete: %v", delDuration)
		assert.Less(t, delDuration.Milliseconds(), int64(50), "Cache delete should be fast")
		
		// 验证删除后获取失败
		_, err = setup.userCache.GetUser(ctx, setup.testUserID)
		// 使用更宽松的错误检查，因为不同的Redis客户端可能返回不同的错误类型
		assert.Error(t, err, "Should return error after deletion")
		// 检查错误信息是否包含任何表示"无数据"的关键词
		errorMsg := err.Error()
		assert.True(t, 
			strings.Contains(errorMsg, "nil") ||
			strings.Contains(errorMsg, "not found") ||
			strings.Contains(errorMsg, "no such key") ||
			strings.Contains(errorMsg, "unexpected end"),
			"Should indicate no data found. Got: %s", errorMsg)
	})
}

// TestStandaloneOptimizedDAO 独立优化DAO测试
func TestStandaloneOptimizedDAO(t *testing.T) {
	setup := setupStandaloneTest(t)
	ctx := context.Background()
	
	t.Run("Optimized DAO Basic Operations", func(t *testing.T) {
		// 测试GetUserBasicByKey
		start := time.Now()
		userBasic, err := setup.dao.UserOptimized.GetUserBasicByKey(ctx, setup.testUserKey)
		duration := time.Since(start)
		
		t.Logf("GetUserBasicByKey: %v", duration)
		
		if err == mysql.ErrNotFound {
			t.Log("User not found (expected for test)")
		} else if err != nil {
			t.Logf("GetUserBasicByKey error: %v", err)
		} else {
			assert.NotNil(t, userBasic)
			assert.Equal(t, setup.testUserKey, userBasic.Key)
		}
		
		// 基本性能要求（考虑网络延迟）
		assert.Less(t, duration.Milliseconds(), int64(2000), "Query should complete within 2s")
	})
}

// TestStandaloneCacheVsDatabase 独立缓存vs数据库测试
func TestStandaloneCacheVsDatabase(t *testing.T) {
	setup := setupStandaloneTest(t)
	ctx := context.Background()
	
	// 创建测试用户对象
	testUser := &domain.User{
		UserBasic: &domain.UserBasic{
			ID:       setup.testUserID,
			Key:      setup.testUserKey,
			Username: "test_performance",
			Secret:   "$2a$10$hash",
		},
		UserEntityConnections: []*domain.UserEntityConnection{},
	}
	
	t.Run("Cache vs Database Performance Comparison", func(t *testing.T) {
		// 预热缓存
		err := setup.userCache.SetUser(ctx, setup.testUserID, testUser)
		assert.NoError(t, err)
		
		// 测试数据库查询（多次以获得平均值）
		var dbTotalDuration time.Duration
		dbQueries := 3
		
		for i := 0; i < dbQueries; i++ {
			start := time.Now()
			_, err := setup.dao.UserOptimized.GetUserBasicByKey(ctx, setup.testUserKey)
			dbTotalDuration += time.Since(start)
			
			if err != nil && err != mysql.ErrNotFound {
				t.Logf("Database query %d failed: %v", i, err)
			}
		}
		dbAvgDuration := dbTotalDuration / time.Duration(dbQueries)
		
		// 测试缓存查询（多次以获得平均值）
		var cacheTotalDuration time.Duration
		cacheQueries := 10
		
		for i := 0; i < cacheQueries; i++ {
			start := time.Now()
			_, err := setup.userCache.GetUser(ctx, setup.testUserID)
			cacheTotalDuration += time.Since(start)
			
			assert.NoError(t, err)
		}
		cacheAvgDuration := cacheTotalDuration / time.Duration(cacheQueries)
		
		t.Logf("Database average: %v", dbAvgDuration)
		t.Logf("Cache average: %v", cacheAvgDuration)
		
		// 缓存应该比数据库快
		assert.Less(t, cacheAvgDuration, dbAvgDuration, "Cache should be faster than database")
		
		// 计算性能提升
		if dbAvgDuration > 0 {
			improvement := float64(dbAvgDuration-cacheAvgDuration) / float64(dbAvgDuration) * 100
			t.Logf("Cache performance improvement: %.2f%%", improvement)
			
			// 缓存应该有显著的性能提升
			assert.Greater(t, improvement, 0.0, "Cache should provide performance improvement")
		}
	})
}

// TestStandaloneIndexEffectiveness 独立索引效果测试
func TestStandaloneIndexEffectiveness(t *testing.T) {
	setup := setupStandaloneTest(t)
	ctx := context.Background()
	
	t.Run("Index Query Performance", func(t *testing.T) {
		// 测试按key查询（应该使用索引）
		queries := 3
		var totalDuration time.Duration
		
		for i := 0; i < queries; i++ {
			start := time.Now()
			_, err := setup.dao.UserOptimized.GetUserBasicByKey(ctx, setup.testUserKey)
			totalDuration += time.Since(start)
			
			if err != nil && err != mysql.ErrNotFound {
				t.Logf("Index query %d failed: %v", i, err)
			}
		}
		
		avgDuration := totalDuration / time.Duration(queries)
		t.Logf("Indexed query average: %v", avgDuration)
		
		// 索引查询应该相对较快（考虑网络延迟）
		assert.Less(t, avgDuration.Milliseconds(), int64(1000), "Indexed queries should be reasonably fast")
	})
}

// TestStandaloneSystemHealth 独立系统健康测试
func TestStandaloneSystemHealth(t *testing.T) {
	setup := setupStandaloneTest(t)
	ctx := context.Background()
	
	t.Run("Database Connection Health", func(t *testing.T) {
		// 测试数据库连接
		start := time.Now()
		_, err := setup.dao.UserOptimized.GetUserBasicByKey(ctx, "health_check_user")
		duration := time.Since(start)
		
		t.Logf("Database health check: %v", duration)
		
		// 应该返回错误（NotFound或其他），但不应该是连接错误
		if err != nil {
			// 检查是否是预期的错误类型
			assert.True(t, 
				err == mysql.ErrNotFound || 
				strings.Contains(err.Error(), "no rows") ||
				strings.Contains(err.Error(), "not found"),
				"Should get NotFound or similar error, not connection error. Got: %v", err)
		}
		
		// 健康检查应该快速完成
		assert.Less(t, duration.Milliseconds(), int64(2000), "Health check should complete within 2s")
	})
	
	t.Run("Redis Connection Health", func(t *testing.T) {
		// 测试Redis连接
		testUser := &domain.User{
			UserBasic: &domain.UserBasic{
				ID:  types.ID(1),
				Key: "health_check",
			},
		}
		
		start := time.Now()
		err := setup.userCache.SetUser(ctx, types.ID(1), testUser)
		duration := time.Since(start)
		
		t.Logf("Redis health check: %v", duration)
		assert.NoError(t, err, "Redis should be connected")
		
		// Redis操作应该很快
		assert.Less(t, duration.Milliseconds(), int64(100), "Redis operations should be fast")
		
		// 清理
		setup.userCache.DelUser(ctx, types.ID(1))
	})
}

// TestStandalonePerformanceMetrics 独立性能指标测试
func TestStandalonePerformanceMetrics(t *testing.T) {
	setup := setupStandaloneTest(t)
	ctx := context.Background()
	
	testUser := &domain.User{
		UserBasic: &domain.UserBasic{
			ID:       setup.testUserID,
			Key:      setup.testUserKey,
			Username: "test_performance",
			Secret:   "$2a$10$hash",
		},
		UserEntityConnections: []*domain.UserEntityConnection{},
	}
	
	t.Run("Performance Metrics Collection", func(t *testing.T) {
		metrics := make(map[string]time.Duration)
		
		// 缓存操作性能指标
		start := time.Now()
		setup.userCache.SetUser(ctx, setup.testUserID, testUser)
		metrics["cache_set"] = time.Since(start)
		
		start = time.Now()
		setup.userCache.GetUser(ctx, setup.testUserID)
		metrics["cache_get"] = time.Since(start)
		
		start = time.Now()
		setup.userCache.DelUser(ctx, setup.testUserID)
		metrics["cache_del"] = time.Since(start)
		
		// 数据库操作性能指标
		start = time.Now()
		setup.dao.UserOptimized.GetUserBasicByKey(ctx, setup.testUserKey)
		metrics["db_query"] = time.Since(start)
		
		// 输出所有性能指标
		t.Log("=== Performance Metrics ===")
		for operation, duration := range metrics {
			t.Logf("%-12s: %v", operation, duration)
		}
		
		// 验证性能要求
		assert.Less(t, metrics["cache_set"].Milliseconds(), int64(50), "Cache set should be fast")
		assert.Less(t, metrics["cache_get"].Milliseconds(), int64(10), "Cache get should be very fast")
		assert.Less(t, metrics["cache_del"].Milliseconds(), int64(50), "Cache delete should be fast")
		assert.Less(t, metrics["db_query"].Milliseconds(), int64(1000), "Database query should complete within 1s")
		
		// 计算缓存vs数据库的性能比较
		if metrics["db_query"] > 0 && metrics["cache_get"] > 0 {
			speedup := float64(metrics["db_query"]) / float64(metrics["cache_get"])
			t.Logf("Cache is %.2fx faster than database", speedup)
			
			assert.Greater(t, speedup, 1.0, "Cache should be faster than database")
		}
	})
}
